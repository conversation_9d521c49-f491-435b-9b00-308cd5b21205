@echo off
title Port 8888 FRP Tunnel

echo ========================================
echo    Port 8888 FRP Tunnel Launcher
echo ========================================
echo.

echo Starting FRP tunnel service...
echo    - Local port: 8888
echo    - Remote port: 8888
echo    - Server: *************:7000
echo.

echo Access URLs after startup:
echo    - Local: http://localhost:8888
echo    - Remote: http://*************:8888
echo.

echo Generating FRP config file...
(
echo # Port 8888 FRP config
echo serverAddr = "*************"
echo serverPort = 7000
echo.
echo # Log config
echo log.to = "./frpc_port8888.log"
echo log.level = "info"
echo.
echo # Port 8888 HTTP tunnel
echo [[proxies]]
echo name = "port-8888-http"
echo type = "tcp"
echo localIP = "127.0.0.1"
echo localPort = 8888
echo remotePort = 8888
) > "frp_0.52.3_windows_amd64\frpc_port8888.toml"

echo Starting FRP client...
start "FRP Client - Port 8888" "frp_0.52.3_windows_amd64\frpc.exe" -c "frp_0.52.3_windows_amd64\frpc_port8888.toml"

timeout /t 2 >nul

echo.
echo FRP tunnel started successfully!
echo.
echo Service status:
echo    - FRP client: Started (running in background)
echo    - Local port: 8888 -^> Remote port: 8888
echo    - Log file: frp_0.52.3_windows_amd64\frpc_port8888.log
echo.

echo Access methods:
echo    - Local access: http://localhost:8888
echo    - Remote access: http://*************:8888
echo.

echo Usage instructions:
echo    1. Make sure a service is running on local port 8888
echo    2. Use remote address to access local service from internet
echo    3. Closing this window will NOT stop FRP service
echo    4. To stop FRP service, close the FRP Client window
echo.

echo Notes:
echo    - Ensure local firewall allows port 8888
echo    - Check network connection if connection fails
echo    - FRP server address: *************:7000
echo.

echo Press any key to exit this window (FRP service will continue running)...
pause >nul