@echo off
chcp 65001 >nul
title 停止 Highlight AI 服务

echo ========================================
echo      停止 Highlight AI 服务
echo ========================================
echo.

echo 🛑 正在停止服务...

echo ⚡ 停止 FRP 客户端...
taskkill /f /im frpc.exe >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ FRP 客户端已停止
) else (
    echo ⚠️  未找到运行中的 FRP 客户端
)

echo 🐍 停止 Python 服务...
for /f "tokens=2" %%i in ('tasklist /fi "imagename eq python.exe" /fo table /nh 2^>nul') do (
    taskkill /f /pid %%i >nul 2>&1
)
if %errorlevel% equ 0 (
    echo ✅ Python 服务已停止
) else (
    echo ⚠️  未找到运行中的 Python 服务
)

echo 🧹 清理临时文件...
del "..\frp_0.52.3_windows_amd64\frpc_highlight2api.toml" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 临时配置文件已清理
)

echo.
echo ✅ Highlight AI 服务已完全停止！
echo.

timeout /t 3 >nul
echo 窗口将在3秒后自动关闭...
