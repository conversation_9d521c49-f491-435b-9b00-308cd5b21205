# Highlight AI API 一键启动说明

## 🚀 快速启动

### 方式一：使用管理器（推荐）
双击运行 `一键启动.bat`，通过菜单选择启动方式。

### 方式二：直接启动
- **前台模式**：双击 `start_highlight2api_frp.bat`
- **后台模式**：双击 `start_highlight2api_background.bat`

## 📋 服务信息

- **本地端口**：8080
- **远程端口**：8082
- **FRP 服务器**：*************:7000

## 🌐 访问地址

- **本地访问**：http://localhost:8080/v1
- **远程访问**：http://*************:8082/v1
- **健康检查**：http://localhost:8080/health

## 🛑 停止服务

- 双击 `stop_highlight2api.bat`
- 或在管理器中选择停止选项

## 📁 文件说明

| 文件名 | 用途 |
|--------|------|
| `一键启动.bat` | 主管理器，提供菜单选择 |
| `start_highlight2api_frp.bat` | 前台启动（窗口保持打开） |
| `start_highlight2api_background.bat` | 后台启动（可关闭窗口） |
| `stop_highlight2api.bat` | 停止所有服务 |

## ⚙️ 技术细节

- 自动生成 FRP 配置文件
- 自动激活虚拟环境
- 自动安装依赖（支持 uv 和 pip）
- 服务状态检测
- 自动清理临时文件

## 🔧 故障排除

1. **端口被占用**：检查 8080 端口是否被其他程序占用
2. **FRP 连接失败**：检查网络连接和服务器状态
3. **依赖安装失败**：手动运行 `pip install -r requirements.txt`
4. **虚拟环境问题**：确保 venv 文件夹存在且完整
