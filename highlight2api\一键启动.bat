@echo off
chcp 65001 >nul
title Highlight AI 一键启动管理器

:MENU
cls
echo ========================================
echo     Highlight AI 一键启动管理器
echo ========================================
echo.

echo 📊 当前服务状态:
tasklist /fi "imagename eq python.exe" | find "python.exe" >nul
if %errorlevel% equ 0 (
    echo    🟢 Python 服务: 运行中
) else (
    echo    🔴 Python 服务: 未运行
)

tasklist /fi "imagename eq frpc.exe" | find "frpc.exe" >nul
if %errorlevel% equ 0 (
    echo    🟢 FRP 客户端: 运行中
) else (
    echo    🔴 FRP 客户端: 未运行
)

echo.
echo 📋 服务信息:
echo    • 本地端口: 8080
echo    • 远程端口: 8082 (*************)
echo    • 本地访问: http://localhost:8080/v1
echo    • 远程访问: http://*************:8082/v1
echo.

echo 🎯 请选择操作:
echo    [1] 启动服务 (前台模式)
echo    [2] 启动服务 (后台模式)
echo    [3] 停止所有服务
echo    [4] 检查服务状态
echo    [5] 打开本地API地址
echo    [6] 打开远程API地址
echo    [0] 退出
echo.

set /p choice=请输入选项 (0-6): 

if "%choice%"=="1" goto START_FOREGROUND
if "%choice%"=="2" goto START_BACKGROUND
if "%choice%"=="3" goto STOP_SERVICES
if "%choice%"=="4" goto CHECK_STATUS
if "%choice%"=="5" goto OPEN_LOCAL
if "%choice%"=="6" goto OPEN_REMOTE
if "%choice%"=="0" goto EXIT
goto MENU

:START_FOREGROUND
echo.
echo 🚀 启动服务 (前台模式)...
call start_highlight2api_frp.bat
goto MENU

:START_BACKGROUND
echo.
echo 🚀 启动服务 (后台模式)...
call start_highlight2api_background.bat
timeout /t 3 >nul
goto MENU

:STOP_SERVICES
echo.
echo 🛑 停止所有服务...
call stop_highlight2api.bat
timeout /t 3 >nul
goto MENU

:CHECK_STATUS
echo.
echo 📊 检查服务状态...
echo.
echo Python 进程:
tasklist /fi "imagename eq python.exe" 2>nul | find "python.exe" || echo    未找到 Python 进程
echo.
echo FRP 进程:
tasklist /fi "imagename eq frpc.exe" 2>nul | find "frpc.exe" || echo    未找到 FRP 进程
echo.
echo 端口占用情况:
netstat -an | find ":8080" || echo    端口 8080 未被占用
echo.
pause
goto MENU

:OPEN_LOCAL
echo.
echo 🌐 打开本地API地址...
start http://localhost:8080/v1
timeout /t 2 >nul
goto MENU

:OPEN_REMOTE
echo.
echo 🌐 打开远程API地址...
start http://*************:8082/v1
timeout /t 2 >nul
goto MENU

:EXIT
echo.
echo 👋 再见！
timeout /t 2 >nul
exit
