@echo off
chcp 65001 >nul
title Highlight AI + FRP 一键启动

echo ========================================
echo    Highlight AI + FRP 一键启动器
echo ========================================
echo.

echo 🚀 正在启动服务组合...
echo    • Highlight AI API (端口: 8080)
echo    • FRP 内网穿透 (远程端口: 8082)
echo    • 服务器: *************:7000
echo.

echo 📋 启动后访问地址:
echo    • 本地: http://localhost:8080/v1
echo    • 远程: http://*************:8082/v1
echo    • 健康检查: http://localhost:8080/health
echo.

echo ⚡ 正在生成临时FRP配置...
(
echo # Highlight AI专用FRP配置
echo serverAddr = "*************"
echo serverPort = 7000
echo.
echo # 日志配置
echo log.to = "./frpc_highlight2api.log"
echo log.level = "info"
echo.
echo # Highlight AI HTTP隧道
echo [[proxies]]
echo name = "highlight2api-http"
echo type = "tcp"
echo localIP = "127.0.0.1"
echo localPort = 8080
echo remotePort = 8082
) > "..\frp_0.52.3_windows_amd64\frpc_highlight2api.toml"

echo ⚡ 正在启动FRP客户端...
start "FRP Client - Highlight AI" /min "..\frp_0.52.3_windows_amd64\frpc.exe" -c "..\frp_0.52.3_windows_amd64\frpc_highlight2api.toml"

timeout /t 3 >nul

echo 🔧 激活虚拟环境...
if exist "venv\Scripts\activate.bat" (
    call venv\Scripts\activate.bat
    echo ✅ 虚拟环境已激活
) else (
    echo ⚠️  未找到虚拟环境，使用系统Python
)

echo 📦 检查并安装依赖...
python -c "import fastapi, uvicorn, httpx, loguru" 2>nul
if %errorlevel% neq 0 (
    echo 📥 安装依赖包...
    if exist "pyproject.toml" (
        echo 使用 uv 安装依赖...
        uv sync 2>nul || (
            echo uv 不可用，使用 pip 安装...
            pip install fastapi uvicorn httpx loguru sse-starlette pycryptodome requests
        )
    ) else (
        pip install fastapi uvicorn httpx loguru sse-starlette pycryptodome requests
    )
)

echo.
echo 🚀 启动Highlight AI服务...
echo    本地服务: http://localhost:8080/v1
echo    FRP转发: http://*************:8082/v1
echo    健康检查: http://localhost:8080/health
echo.
echo 💡 提示: 可以关闭此窗口，服务将在后台继续运行
echo    要停止服务，请重新运行此脚本或手动结束进程
echo.

python main.py

echo.
echo ⚠️  服务已停止，正在清理...
taskkill /f /im frpc.exe >nul 2>&1
del "..\frp_0.52.3_windows_amd64\frpc_highlight2api.toml" >nul 2>&1

pause
