# 2API 项目本地运行文档

## 项目概述

2API 项目包含5个独立的API服务子项目，每个项目都提供不同的AI模型代理服务。本文档详细说明如何在本地环境中运行这些项目（不使用FRP内网穿透）。
Docker 官方文档：容器内连接宿主机服务，使用 host.docker.internal。不要直接localhost，否则链接的是容器内的localhost。

---

## 1. Turnstile-Solver

### 项目描述
Turnstile验证码解决方案，提供两个API服务：
- Turnstile Solver API (端口5000)
- TenBinAI OpenAI API (端口8003)

### 技术栈
- **语言**: Python
- **框架**: FastAPI, Quart
- **依赖管理**: requirements.txt
- **虚拟环境**: 有 (venv文件夹)

### 端口配置
- **Turnstile Solver API**: 5000
- **TenBinAI OpenAI API**: 8003

### 本地运行步骤

#### 方法1: 使用虚拟环境
```bash
cd Turnstile-Solver

# 激活虚拟环境 (Windows)
venv\Scripts\activate

# 安装依赖 (如果需要)
pip install -r requirements.txt

# 启动Turnstile Solver API (端口5000)
python api_solver.py --port 5000

# 启动TenBinAI OpenAI API (端口8003) - 新开命令行窗口
python main.py
```

#### 方法2: 直接运行
```bash
cd Turnstile-Solver

# 安装依赖
pip install -r requirements.txt

# 启动服务
python api_solver.py --port 5000
python main.py  # 新开命令行窗口
```

### 访问地址
- Turnstile Solver API: http://localhost:5000
- TenBinAI OpenAI API: http://localhost:8003/v1

### 配置文件
- `client_api_keys.json`: 客户端API密钥
- `tenbin.json`: TenBin账户配置
- `models.json`: 模型配置
- `proxies.txt`: 代理配置

---

## 2. copilot-api-master

### 项目描述
GitHub Copilot API代理服务，将GitHub Copilot转换为OpenAI兼容的API。

### 技术栈
- **语言**: TypeScript/Node.js
- **运行时**: Bun (推荐) 或 Node.js
- **框架**: Hono
- **依赖管理**: package.json

### 端口配置
- **默认端口**: 4141

### 本地运行步骤

#### 使用Bun (推荐)
```bash
cd copilot-api-master

# 安装依赖
bun install

# 启动开发服务器
bun run dev

# 或启动生产服务器
bun run start
```

#### 使用Node.js
```bash
cd copilot-api-master

# 安装依赖
npm install

# 构建项目
npm run build

# 启动服务器
npm start
```

#### 使用自定义端口
```bash
# 使用bun启动并指定端口
bun run src/main.ts start --port 4141

# 或使用node
node dist/main.js start --port 4141
```

### 访问地址
- API服务: http://localhost:4141

### 前置要求
1. **GitHub认证**: 需要先运行认证命令
   ```bash
   bun run src/main.ts auth
   # 或
   node dist/main.js auth
   ```
2. **GitHub Copilot订阅**: 需要有效的GitHub Copilot订阅

### 配置说明
- 首次运行需要进行GitHub OAuth认证
- 支持个人版、商业版、企业版GitHub账户
- 可配置速率限制和手动审批模式

---

## 3. highlight2api

### 项目描述
Highlight AI API代理服务，提供OpenAI兼容的API接口。

### 技术栈
- **语言**: Python
- **框架**: FastAPI
- **包管理器**: uv (推荐) 或 pip
- **虚拟环境**: 有 (venv文件夹)

### 端口配置
- **服务端口**: 8080

### 本地运行步骤

#### 方法1: 使用uv (推荐)
```bash
cd highlight2api

# 激活虚拟环境
venv\Scripts\activate  # Windows
# 或 source venv/bin/activate  # Linux/Mac

# 使用uv安装依赖
uv sync

# 启动服务
python main.py
```

#### 方法2: 使用pip
```bash
cd highlight2api

# 激活虚拟环境
venv\Scripts\activate  # Windows

# 安装依赖
pip install fastapi uvicorn httpx loguru sse-starlette pycryptodome requests

# 启动服务
python main.py
```

#### 方法3: 使用uvicorn直接启动
```bash
cd highlight2api

# 激活虚拟环境
venv\Scripts\activate

# 使用uvicorn启动
uvicorn main:app --host 0.0.0.0 --port 8080 --reload
```

### 访问地址
- API服务: http://localhost:8080/v1
- 健康检查: http://localhost:8080/health

### 配置说明
- 需要Highlight AI账户的认证信息
- 支持多种AI模型代理

---

## 4. ki2api

### 项目描述
Claude Sonnet 4 OpenAI兼容API，通过AWS CodeWhisperer提供服务。

### 技术栈
- **语言**: Python
- **框架**: FastAPI
- **依赖管理**: requirements.txt

### 端口配置
- **服务端口**: 8989

### 本地运行步骤

#### 基本运行
```bash
cd ki2api

# 安装依赖
pip install -r requirements.txt

# 启动服务
python app.py
```

#### 使用虚拟环境 (推荐)
```bash
cd ki2api

# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
venv\Scripts\activate  # Windows
# 或 source venv/bin/activate  # Linux/Mac

# 安装依赖
pip install -r requirements.txt

# 启动服务
python app.py
```

### 访问地址
- API服务: http://localhost:8989/v1
- 模型列表: http://localhost:8989/v1/models

### 环境变量配置
需要在项目根目录创建 `.env` 文件或设置环境变量：

```env
API_KEY=ki2api-key-2024
KIRO_ACCESS_TOKEN=your_access_token_here
KIRO_REFRESH_TOKEN=your_refresh_token_here
```

### 获取Token方法
```bash
# 使用token_reader.py获取token
python token_reader.py
```

---

## 5. kilo2api

### 项目描述
KiloCode AI API适配器，将KiloCode AI转换为OpenAI API格式。

### 技术栈
- **语言**: Python
- **框架**: FastAPI
- **依赖管理**: requirements.txt

### 端口配置
- **服务端口**: 8001

### 本地运行步骤

#### 基本运行
```bash
cd kilo2api

# 安装依赖
pip install -r requirements.txt

# 启动服务
python main.py
```

#### 使用虚拟环境 (推荐)
```bash
cd kilo2api

# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
venv\Scripts\activate  # Windows
# 或 source venv/bin/activate  # Linux/Mac

# 安装依赖
pip install -r requirements.txt

# 启动服务
python main.py
```

### 访问地址
- API服务: http://localhost:8001/v1
- 模型列表: http://localhost:8001/v1/models

### 配置文件

#### 1. KiloCode JWT配置 (`kilocode.json`)
```json
[
    {
        "jwt": "your-kilocode-jwt-here"
    }
]
```

#### 2. 客户端API密钥配置 (`client_api_keys.json`)
```json
[
    "sk-client-key-1",
    "sk-client-key-2"
]
```

#### 3. 模型配置 (`models.json`)
```json
[
    "anthropic-claude-3.7-sonnet",
    "anthropic-claude-4-sonnet",
    "google-chat-gemini-pro-2.5",
    "openai-o4-mini",
    "openai-o3-mini",
    "openai-o3",
    "openai-o1",
    "openai-gpt-4o",
    "anthropic-claude-3.5-sonnet",
    "openai-gpt4.1"
]
```

---

## 端口总览

| 项目 | 服务 | 端口 | 访问地址 |
|------|------|------|----------|
| Turnstile-Solver | Turnstile Solver API | 5000 | http://localhost:5000 |
| Turnstile-Solver | TenBinAI OpenAI API | 8003 | http://localhost:8003/v1 |
| copilot-api-master | GitHub Copilot API | 4141 | http://localhost:4141 |
| highlight2api | Highlight AI API | 8080 | http://localhost:8080/v1 |
| ki2api | Claude Sonnet 4 API | 8989 | http://localhost:8989/v1 |
| kilo2api | KiloCode AI API | 8001 | http://localhost:8001/v1 |

---

## 常见问题

### 1. 端口冲突
如果端口被占用，可以：
- 检查端口占用：`netstat -ano | findstr :端口号`
- 结束占用进程或修改项目配置使用其他端口：`taskkill /PID 18032 /F`

### 2. Python依赖问题
- 确保使用正确的Python版本 (推荐Python 3.8+)
- 使用虚拟环境避免依赖冲突
- 如果pip安装失败，尝试升级pip：`python -m pip install --upgrade pip`

### 3. Node.js项目问题
- 确保安装了Node.js 16+或Bun
- 清除缓存：`npm cache clean --force` 或 `bun pm cache rm`
- 重新安装依赖：删除node_modules后重新安装

### 4. 权限问题
- Windows环境下可能需要以管理员身份运行命令行
- 确保对项目目录有读写权限

---

## 注意事项

1. **环境要求**：
   - Python 3.8+ (用于Python项目)
   - Node.js 16+ 或 Bun (用于copilot-api-master)
   - Windows环境，避免使用Linux特有命令

2. **配置文件**：
   - 各项目的配置文件需要根据实际情况填写
   - API密钥和Token需要从对应服务获取

3. **网络要求**：
   - 某些服务需要访问外部API
   - 确保网络连接正常

4. **资源占用**：
   - 同时运行多个服务会占用较多系统资源
   - 建议根据需要选择性启动服务

---

## 技术支持

如遇到问题，请检查：
1. 项目目录下的README.md文件
2. 各项目的启动脚本和配置文件
3. 控制台输出的错误信息
4. 确保所有依赖都已正确安装